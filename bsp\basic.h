#ifndef __BASIC_H__
#define __BASIC_H__

#include "bsp_system.h"
#include "uart_bsp.h"
#include "stm32f4xx_hal.h"
#include <stdio.h>
#include <math.h>
#include <stdbool.h>
#include <stdlib.h>

// --- 循迹功能数据结构定义 ---

// 点坐标结构体
typedef struct {
    int x;
    int y;
} Point;

// 循迹状态枚举
typedef enum {
    TRACE_IDLE,           // 空闲状态，准备移动到下一个顶点
    TRACE_MOVING,         // 正在移动到目标点
    TRACE_AT_WAYPOINT,    // 已到达路径点，准备移动到下一个路径点
    TRACE_AT_TARGET,      // 已到达目标点，准备下一步
    TRACE_COMPLETED       // 循迹完成
} TraceState;

// 循迹控制结构体
typedef struct {
    Point vertices[5];        // 矩形顶点数组（5个点，最后一个是回到起始点）
    int current_target;       // 当前目标顶点索引
    TraceState state;         // 当前状态
    int total_vertices;       // 总顶点数
    uint32_t state_start_time; // 状态开始时间（用于超时保护）

    // 沿边框移动支持
    Point waypoints[4];       // 路径点数组（最多4个顶点）
    int waypoint_count;       // 路径点数量
    int current_waypoint;     // 当前路径点索引
    bool use_perimeter_mode;  // 是否使用沿边框模式
    Point initial_position;   // 记录初始位置，用于最后的复位
} TraceController;

// --- 函数声明 ---

/**
 * @brief 激光点移动到矩形左上方顶点
 *
 * 不依赖PID追踪系统，直接使用电机控制移动到目标位置。
 * 这样可以避免自动追踪功能的干扰，便于测试新功能。
 */
void move_laser_to_rectangle_corner(void);

/**
 * @brief 一键循迹功能
 *
 * 自动完成完整的循迹流程：
 * 1. 先移动到矩形左上角（确保起始位置正确）
 * 2. 然后开始矩形循迹过程
 * 用户只需按一次按键即可完成所有操作。
 */
void move_to_corner_and_start_trace(void);



/**
 * @brief 停止矩形循迹
 *
 * 停止当前的循迹过程并清理状态。
 */
void stop_rectangle_trace(void);

/**
 * @brief 循迹处理函数（状态机）
 *
 * 非阻塞的循迹状态机，需要在调度器中定期调用。
 * 负责管理循迹过程的各个状态转换。
 */
void trace_proc(void);

/**
 * @brief 获取当前激光位置
 *
 * @return Point 当前红色激光的位置坐标
 */
Point get_current_laser_position(void);

/**
 * @brief 计算两点间距离
 *
 * @param p1 第一个点
 * @param p2 第二个点
 * @return float 两点间的欧几里得距离
 */
float calculate_distance(Point p1, Point p2);



#endif
