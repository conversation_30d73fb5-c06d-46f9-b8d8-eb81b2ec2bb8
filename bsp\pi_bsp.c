#include "pi_bsp.h"

// 默认值设为无效状态，X, Y 为 0
LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};
LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0};
RectCoord_t latest_rect_coord = {0};
FourPointCoord_t latest_four_point_coord = {0};

// 扩展的MaixCam数据解析函数
// 支持格式：red:(x,y), gre:(x,y), rect:(x1,y1,x2,y2,x3,y3,x4,y4), point:(index,x,y)
int pi_parse_data(char *buffer)
{
    if (!buffer)
        return -1; // 空指针检查

    int parsed_count;

    // 尝试匹配 "red:(x,y)" 格式
    if (strncmp(buffer, "red:", 4) == 0)
    {
        int parsed_x, parsed_y;
        parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2; // 解析失败

        // 解析成功，更新全局红色激光坐标
        latest_red_laser_coord.x = parsed_x;
        latest_red_laser_coord.y = parsed_y;
        latest_red_laser_coord.isValid = 1;

       // my_printf(&huart1, "解析红色激光成功: X=%d, Y=%d\r\n", latest_red_laser_coord.x, latest_red_laser_coord.y);
    }
    // 尝试匹配 "gre:(x,y)" 格式
    else if (strncmp(buffer, "gre:", 4) == 0)
    {
        int parsed_x, parsed_y;
        parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
        if (parsed_count != 2)
            return -2; // 解析失败

        // 解析成功，更新全局绿色激光坐标
        latest_green_laser_coord.x = parsed_x;
        latest_green_laser_coord.y = parsed_y;
        latest_green_laser_coord.isValid = 1;

        my_printf(&huart1, "解析绿色激光成功: X=%d, Y=%d\r\n", latest_green_laser_coord.x, latest_green_laser_coord.y);
    }
    // 新增：尝试匹配 "rect:(x1,y1,x2,y2,x3,y3,x4,y4)" 格式
    else if (strncmp(buffer, "rect:", 5) == 0)
    {
        parsed_count = sscanf(buffer, "rect:(%d,%d,%d,%d,%d,%d,%d,%d)",
                             &latest_rect_coord.x[0], &latest_rect_coord.y[0],
                             &latest_rect_coord.x[1], &latest_rect_coord.y[1],
                             &latest_rect_coord.x[2], &latest_rect_coord.y[2],
                             &latest_rect_coord.x[3], &latest_rect_coord.y[3]);
        if (parsed_count != 8)
            return -2; // 解析失败

        latest_rect_coord.isValid = 1;
        // my_printf(&huart1, "Parsed RECT: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
        //           latest_rect_coord.x[0], latest_rect_coord.y[0],
        //           latest_rect_coord.x[1], latest_rect_coord.y[1],
        //           latest_rect_coord.x[2], latest_rect_coord.y[2],
        //           latest_rect_coord.x[3], latest_rect_coord.y[3]);
    }
    // 新增：尝试匹配 "point:(index,x,y)" 格式
    else if (strncmp(buffer, "point:", 6) == 0)
    {
        parsed_count = sscanf(buffer, "point:(%d,%d,%d)",
                             &latest_four_point_coord.index,
                             &latest_four_point_coord.x,
                             &latest_four_point_coord.y);
        if (parsed_count != 3)
            return -2; // 解析失败

        latest_four_point_coord.isValid = 1;
        // my_printf(&huart1, "Parsed POINT: Index=%d, X=%d, Y=%d\r\n",
        //           latest_four_point_coord.index,
        //           latest_four_point_coord.x,
        //           latest_four_point_coord.y);
    }
    else
    {
        // 未知格式或无效数据
        return -3; // 未知或无效格式
    }

    return 0; // 成功
}



/**
 * @brief 检查摄像头数据状态
 */
void check_camera_data_status(void)
{
    static uint32_t last_check_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每5秒检查一次数据状态
    if (current_time - last_check_time > 5000) {
        my_printf(&huart1, "=== 摄像头数据状态检查 ===\r\n");
        my_printf(&huart1, "红色激光: isValid=%d, 坐标=(%d,%d)\r\n",
                  latest_red_laser_coord.isValid,
                  latest_red_laser_coord.x, latest_red_laser_coord.y);
        my_printf(&huart1, "绿色激光: isValid=%d, 坐标=(%d,%d)\r\n",
                  latest_green_laser_coord.isValid,
                  latest_green_laser_coord.x, latest_green_laser_coord.y);
        my_printf(&huart1, "矩形数据: isValid=%d\r\n", latest_rect_coord.isValid);
        my_printf(&huart1, "四点数据: isValid=%d\r\n", latest_four_point_coord.isValid);
        my_printf(&huart1, "========================\r\n");
        last_check_time = current_time;
    }
}

void pi_proc(void)
{
	float pos_out_x,pos_out_y=0;

	// 检查摄像头数据状态
	check_camera_data_status();

		pos_out_x = pid_calc(&pid_x,latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
		pos_out_y = pid_calc(&pid_y,latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
		Step_Motor_Set_Speed_my(-pos_out_x,pos_out_y);


}

