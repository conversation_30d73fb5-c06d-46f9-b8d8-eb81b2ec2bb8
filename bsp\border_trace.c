#include "border_trace.h"
#include "step_motor_bsp.h"
#include "uart_bsp.h"

// --- 全局变量定义 ---
BorderTraceController border_ctrl = {0};

// --- 内部函数声明 ---
static bool calculate_inner_vertices(void);
static int find_closest_path_point(Point current_pos);
static Point calculate_border_center_point(Point outer, Point inner);
static bool is_point_inside_polygon(Point point, Point* vertices, int vertex_count);
static void reset_border_controller(void);

// --- 公共函数实现 ---

/**
 * @brief 初始化边框循迹控制器
 */
void border_trace_init(void)
{
    memset(&border_ctrl, 0, sizeof(BorderTraceController));
    
    // 设置默认参数
    border_ctrl.kp_border = 0.025f;  // 边框模式使用稍高的增益
    border_ctrl.lookahead_distance = BORDER_LOOKAHEAD_DIST;
    border_ctrl.state = BORDER_TRACE_IDLE;
    border_ctrl.is_active = false;
    border_ctrl.emergency_stop = false;
    
    my_printf(&huart1, "边框循迹控制器初始化完成\r\n");
}

/**
 * @brief 从矩形数据生成边框区域
 */
bool generate_border_region(RectCoord_t* rect_data)
{
    if (!rect_data || !rect_data->isValid) {
        my_printf(&huart1, "错误：矩形数据无效\r\n");
        return false;
    }
    
    // 复制外边界顶点
    for (int i = 0; i < 4; i++) {
        border_ctrl.region.outer_vertices[i].x = rect_data->x[i];
        border_ctrl.region.outer_vertices[i].y = rect_data->y[i];
    }
    
    // 计算内边界顶点
    if (!calculate_inner_vertices()) {
        my_printf(&huart1, "错误：无法计算内边界顶点\r\n");
        return false;
    }
    
    border_ctrl.region.is_valid = true;
    
    my_printf(&huart1, "边框区域生成成功\r\n");
    my_printf(&huart1, "外边界: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
              (int)border_ctrl.region.outer_vertices[0].x, (int)border_ctrl.region.outer_vertices[0].y,
              (int)border_ctrl.region.outer_vertices[1].x, (int)border_ctrl.region.outer_vertices[1].y,
              (int)border_ctrl.region.outer_vertices[2].x, (int)border_ctrl.region.outer_vertices[2].y,
              (int)border_ctrl.region.outer_vertices[3].x, (int)border_ctrl.region.outer_vertices[3].y);
    
    return true;
}

/**
 * @brief 生成边框中心线路径
 */
bool generate_border_path(void)
{
    if (!border_ctrl.region.is_valid) {
        my_printf(&huart1, "错误：边框区域未初始化\r\n");
        return false;
    }
    
    border_ctrl.path.path_length = 0;
    border_ctrl.path.total_distance = 0.0f;
    
    // 生成四条边的中心线路径点
    for (int edge = 0; edge < 4; edge++) {
        int next_edge = (edge + 1) % 4;
        
        Point outer_start = border_ctrl.region.outer_vertices[edge];
        Point outer_end = border_ctrl.region.outer_vertices[next_edge];
        Point inner_start = border_ctrl.region.inner_vertices[edge];
        Point inner_end = border_ctrl.region.inner_vertices[next_edge];
        
        // 计算边长
        float edge_length = border_distance(outer_start, outer_end);
        int points_on_edge = (int)(edge_length / BORDER_PATH_SPACING) + 1;
        
        // 在这条边上生成路径点
        for (int i = 0; i < points_on_edge && border_ctrl.path.path_length < MAX_BORDER_PATH_POINTS - 1; i++) {
            float t = (float)i / (points_on_edge - 1);
            
            // 在外边界上的插值点
            Point outer_point = {
                outer_start.x + t * (outer_end.x - outer_start.x),
                outer_start.y + t * (outer_end.y - outer_start.y)
            };
            
            // 在内边界上的插值点
            Point inner_point = {
                inner_start.x + t * (inner_end.x - inner_start.x),
                inner_start.y + t * (inner_end.y - inner_start.y)
            };
            
            // 计算中心点
            Point center_point = calculate_border_center_point(outer_point, inner_point);
            
            // 添加到路径
            border_ctrl.path.path_points[border_ctrl.path.path_length] = center_point;
            
            // 计算累积距离
            if (border_ctrl.path.path_length > 0) {
                float segment_dist = border_distance(
                    border_ctrl.path.path_points[border_ctrl.path.path_length - 1],
                    center_point
                );
                border_ctrl.path.total_distance += segment_dist;
            }
            
            border_ctrl.path.path_length++;
        }
    }
    
    // 闭合路径（添加起始点）
    if (border_ctrl.path.path_length > 0 && border_ctrl.path.path_length < MAX_BORDER_PATH_POINTS) {
        border_ctrl.path.path_points[border_ctrl.path.path_length] = border_ctrl.path.path_points[0];
        border_ctrl.path.path_length++;
    }
    
    border_ctrl.path.is_generated = true;
    border_ctrl.path.current_index = 0;
    
    my_printf(&huart1, "边框路径生成完成，共%d个点，总长度%.1f像素\r\n", 
              border_ctrl.path.path_length, border_ctrl.path.total_distance);
    
    return true;
}

/**
 * @brief 检查点是否在边框区域内
 */
bool is_point_in_border_region(Point point)
{
    if (!border_ctrl.region.is_valid) {
        return false;
    }
    
    // 检查是否在外边界内
    bool in_outer = is_point_inside_polygon(point, border_ctrl.region.outer_vertices, 4);
    
    // 检查是否在内边界外
    bool outside_inner = !is_point_inside_polygon(point, border_ctrl.region.inner_vertices, 4);
    
    return in_outer && outside_inner;
}

/**
 * @brief 找到路径上的前瞻目标点
 */
Point find_border_lookahead_target(Point current_pos)
{
    if (!border_ctrl.path.is_generated || border_ctrl.path.path_length == 0) {
        return current_pos;
    }
    
    // 找到最近的路径点
    int closest_idx = find_closest_path_point(current_pos);
    
    // 从最近点开始，找到满足前瞻距离的点
    float accumulated_dist = 0.0f;
    int target_idx = closest_idx;
    
    while (accumulated_dist < border_ctrl.lookahead_distance) {
        int next_idx = (target_idx + 1) % border_ctrl.path.path_length;
        float segment_dist = border_distance(
            border_ctrl.path.path_points[target_idx],
            border_ctrl.path.path_points[next_idx]
        );
        
        accumulated_dist += segment_dist;
        target_idx = next_idx;
        
        // 防止无限循环
        if (target_idx == closest_idx) break;
    }
    
    border_ctrl.path.current_index = target_idx;
    return border_ctrl.path.path_points[target_idx];
}

/**
 * @brief 启动边框循迹
 */
bool start_border_trace(void)
{
    // 检查前置条件
    if (!latest_rect_coord.isValid) {
        my_printf(&huart1, "错误：未检测到有效矩形数据\r\n");
        return false;
    }

    if (!latest_red_laser_coord.isValid) {
        my_printf(&huart1, "错误：未检测到红色激光点\r\n");
        return false;
    }

    // 重置控制器
    reset_border_controller();

    // 生成边框区域
    if (!generate_border_region(&latest_rect_coord)) {
        return false;
    }

    // 生成边框路径
    if (!generate_border_path()) {
        return false;
    }

    // 激活控制器
    border_ctrl.is_active = true;
    border_ctrl.state = BORDER_TRACE_INIT;
    border_ctrl.state_start_time = HAL_GetTick();

    my_printf(&huart1, "=== 边框循迹启动成功 ===\r\n");
    return true;
}

/**
 * @brief 简单的移动到左上角功能（测试用）
 */
bool start_move_to_left_top_corner(void)
{
    // 检查数据有效性
    if (!latest_rect_coord.isValid) {
        my_printf(&huart1, "错误：矩形数据无效\r\n");
        return false;
    }

    if (!latest_red_laser_coord.isValid) {
        my_printf(&huart1, "错误：激光数据无效\r\n");
        return false;
    }

    // 显示矩形数据
    my_printf(&huart1, "矩形四个顶点:\r\n");
    for (int i = 0; i < 4; i++) {
        my_printf(&huart1, "  顶点%d: (%d, %d)\r\n", i,
                  latest_rect_coord.x[i], latest_rect_coord.y[i]);
    }

    // 找到左上角（Y值最小，如果Y相同则X最小）
    int left_top_index = 0;
    for (int i = 1; i < 4; i++) {
        if (latest_rect_coord.y[i] < latest_rect_coord.y[left_top_index] ||
            (latest_rect_coord.y[i] == latest_rect_coord.y[left_top_index] &&
             latest_rect_coord.x[i] < latest_rect_coord.x[left_top_index])) {
            left_top_index = i;
        }
    }

    // 设置目标点
    border_ctrl.target_point.x = latest_rect_coord.x[left_top_index];
    border_ctrl.target_point.y = latest_rect_coord.y[left_top_index];

    my_printf(&huart1, "左上角目标: (%d, %d) [索引%d]\r\n",
              (int)border_ctrl.target_point.x, (int)border_ctrl.target_point.y, left_top_index);
    my_printf(&huart1, "当前位置: (%d, %d)\r\n",
              latest_red_laser_coord.x, latest_red_laser_coord.y);

    // 启动移动
    border_ctrl.is_active = true;
    border_ctrl.state = BORDER_TRACE_MOVING;

    my_printf(&huart1, "开始移动到左上角\r\n");
    return true;
}

/**
 * @brief 简单的移动到右上角功能（测试用）
 */
bool start_move_to_right_top_corner(void)
{
    // 检查数据有效性
    if (!latest_rect_coord.isValid) {
        my_printf(&huart1, "错误：矩形数据无效\r\n");
        return false;
    }

    if (!latest_red_laser_coord.isValid) {
        my_printf(&huart1, "错误：激光数据无效\r\n");
        return false;
    }

    // 显示矩形数据（简化）
    my_printf(&huart1, "矩形顶点: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
              latest_rect_coord.x[0], latest_rect_coord.y[0],
              latest_rect_coord.x[1], latest_rect_coord.y[1],
              latest_rect_coord.x[2], latest_rect_coord.y[2],
              latest_rect_coord.x[3], latest_rect_coord.y[3]);

    // 找到右上角（Y值最小，如果Y相同则X最大）
    int right_top_index = 0;
    for (int i = 1; i < 4; i++) {
        if (latest_rect_coord.y[i] < latest_rect_coord.y[right_top_index] ||
            (latest_rect_coord.y[i] == latest_rect_coord.y[right_top_index] &&
             latest_rect_coord.x[i] > latest_rect_coord.x[right_top_index])) {
            right_top_index = i;
        }
    }

    // 设置目标点
    border_ctrl.target_point.x = latest_rect_coord.x[right_top_index];
    border_ctrl.target_point.y = latest_rect_coord.y[right_top_index];

    my_printf(&huart1, "右上角目标: (%d, %d) 当前: (%d, %d)\r\n",
              (int)border_ctrl.target_point.x, (int)border_ctrl.target_point.y,
              latest_red_laser_coord.x, latest_red_laser_coord.y);

    // 计算移动距离
    int total_dx = border_ctrl.target_point.x - latest_red_laser_coord.x;
    int total_dy = border_ctrl.target_point.y - latest_red_laser_coord.y;
    float total_distance = sqrt(total_dx*total_dx + total_dy*total_dy);

    // 进行路径安全检查
    Point current_pos = {latest_red_laser_coord.x, latest_red_laser_coord.y};
    Point target_pos = {border_ctrl.target_point.x, border_ctrl.target_point.y};

    if (is_direct_path_safe(current_pos, target_pos)) {
        my_printf(&huart1, "使用直线路径\r\n");
        border_ctrl.use_sequence_mode = false;
    } else {
        my_printf(&huart1, "直线路径不安全，使用安全路径序列\r\n");
        Point path_points[10];
        int path_count = generate_safe_path_points(current_pos, target_pos, path_points);

        if (path_count > 0) {
            init_path_sequence(path_points, path_count);
            my_printf(&huart1, "生成%d个安全路径点\r\n", path_count);
        } else {
            my_printf(&huart1, "警告：无法生成安全路径，使用直线路径\r\n");
            border_ctrl.use_sequence_mode = false;
        }
    }

    // 启动移动
    border_ctrl.is_active = true;
    border_ctrl.state = BORDER_TRACE_MOVING;
    border_ctrl.state_start_time = HAL_GetTick();

    my_printf(&huart1, "开始移动到右上角\r\n");
    return true;
}

/**
 * @brief 停止边框循迹
 */
void stop_border_trace(void)
{
    if (!border_ctrl.is_active) {
        my_printf(&huart1, "边框循迹未激活\r\n");
        return;
    }
    
    border_ctrl.is_active = false;
    border_ctrl.state = BORDER_TRACE_IDLE;
    Step_Motor_Stop();
    
    my_printf(&huart1, "边框循迹已停止\r\n");
}

/**
 * @brief 紧急停止边框循迹
 */
void emergency_stop_border_trace(void)
{
    border_ctrl.emergency_stop = true;
    border_ctrl.is_active = false;
    border_ctrl.state = BORDER_TRACE_ERROR;
    Step_Motor_Stop();

    my_printf(&huart1, "!!! 边框循迹紧急停止 !!!\r\n");
}

/**
 * @brief 边框循迹主处理函数（状态机）
 */
void border_trace_proc(void)
{
    if (!border_ctrl.is_active || border_ctrl.emergency_stop) {
        return;
    }

    uint32_t current_time = HAL_GetTick();
    Point current_laser_pos = {latest_red_laser_coord.x, latest_red_laser_coord.y};

    // 检查激光数据有效性
    if (!latest_red_laser_coord.isValid) {
        my_printf(&huart1, "警告：激光数据无效\r\n");
        return;
    }

    switch (border_ctrl.state) {
        case BORDER_TRACE_INIT:
            // 检查激光点是否在边框区域内
            if (is_point_in_border_region(current_laser_pos)) {
                border_ctrl.state = BORDER_TRACE_MOVING;
                border_ctrl.state_start_time = current_time;
                my_printf(&huart1, "激光点在边框内，开始循迹\r\n");
            } else {
                // 激光点不在边框内，需要先移动到边框
                Point nearest_path_point = border_ctrl.path.path_points[0];
                float min_dist = border_distance(current_laser_pos, nearest_path_point);

                for (int i = 1; i < border_ctrl.path.path_length; i++) {
                    float dist = border_distance(current_laser_pos, border_ctrl.path.path_points[i]);
                    if (dist < min_dist) {
                        min_dist = dist;
                        nearest_path_point = border_ctrl.path.path_points[i];
                    }
                }

                // 移动到最近的路径点
                int dx = nearest_path_point.x - current_laser_pos.x;
                int dy = nearest_path_point.y - current_laser_pos.y;

                float x_speed = border_ctrl.kp_border * dx;
                float y_speed = border_ctrl.kp_border * dy;

                // 限制速度
                x_speed = border_clamp(x_speed, -10.0f, 10.0f);
                y_speed = border_clamp(y_speed, -10.0f, 10.0f);

                Step_Motor_Set_Speed_my(-x_speed, y_speed);

                my_printf(&huart1, "移动到边框: 目标(%d,%d) 速度(%d,%d)\r\n",
                          (int)nearest_path_point.x, (int)nearest_path_point.y, (int)x_speed, (int)(-y_speed));
            }
            break;

        case BORDER_TRACE_MOVING:
            // 检查是否使用序列模式
            if (border_ctrl.use_sequence_mode) {
                // 序列模式：依次移动到各个路径点
                if (border_ctrl.current_sequence_index >= border_ctrl.sequence_length) {
                    // 所有序列点都已完成
                    Step_Motor_Stop();
                    my_printf(&huart1, "路径序列完成！\r\n");
                    border_ctrl.state = BORDER_TRACE_COMPLETED;
                    border_ctrl.is_active = false;
                    border_ctrl.use_sequence_mode = false;
                    break;
                }

                // 获取当前序列目标点
                Point current_target = border_ctrl.path_sequence[border_ctrl.current_sequence_index];

                // 计算到当前序列点的距离和方向
                int dx = current_target.x - latest_red_laser_coord.x;
                int dy = current_target.y - latest_red_laser_coord.y;
                float distance = sqrt(dx*dx + dy*dy);

                // 检查是否到达当前序列点
                if (distance <= 5.0f) {
                    border_ctrl.current_sequence_index++;

                    if (border_ctrl.current_sequence_index >= border_ctrl.sequence_length) {
                        Step_Motor_Stop();
                        my_printf(&huart1, "四顶点循迹完成！\r\n");
                        border_ctrl.state = BORDER_TRACE_COMPLETED;
                        border_ctrl.is_active = false;
                        border_ctrl.use_sequence_mode = false;
                    } else {
                        // 继续到下一个顶点
                        Point next_target = border_ctrl.path_sequence[border_ctrl.current_sequence_index];
                        my_printf(&huart1, "到达顶点%d，继续到下一个顶点(%d,%d)\r\n",
                                  border_ctrl.current_sequence_index, (int)next_target.x, (int)next_target.y);
                    }
                    break;
                }

                // 计算控制速度
                float kp = 0.023f;
                float x_speed = kp * dx;
                float y_speed = kp * dy;

                // 限制速度
                if (x_speed > 10.0f) x_speed = 10.0f;
                if (x_speed < -10.0f) x_speed = -10.0f;
                if (y_speed > 10.0f) y_speed = 10.0f;
                if (y_speed < -10.0f) y_speed = -10.0f;

                // 控制电机
                Step_Motor_Set_Speed_my(-x_speed, -y_speed);

                // 调试信息
                my_printf(&huart1, "序列移动: 点%d/%d 目标(%d,%d) 距离%d\r\n",
                          border_ctrl.current_sequence_index + 1, border_ctrl.sequence_length,
                          (int)current_target.x, (int)current_target.y, (int)distance);

            } else {
                // 单点移动模式（原有逻辑）
                if (border_ctrl.target_point.x != 0 || border_ctrl.target_point.y != 0) {
                    // 计算到目标的距离和方向
                    int dx = border_ctrl.target_point.x - latest_red_laser_coord.x;
                    int dy = border_ctrl.target_point.y - latest_red_laser_coord.y;
                    float distance = sqrt(dx*dx + dy*dy);

                    // 检查是否到达目标
                    if (distance <= 5.0f) {
                        Step_Motor_Stop();
                        my_printf(&huart1, "已到达右上角目标位置！\r\n");
                        border_ctrl.state = BORDER_TRACE_COMPLETED;
                        border_ctrl.is_active = false;
                        break;
                    }

                    // 计算控制速度（使用basic.c的参数）
                    float kp = 0.023f;
                    float x_speed = kp * dx;
                    float y_speed = kp * dy;

                    // 限制速度
                    if (x_speed > 10.0f) x_speed = 10.0f;
                    if (x_speed < -10.0f) x_speed = -10.0f;
                    if (y_speed > 10.0f) y_speed = 10.0f;
                    if (y_speed < -10.0f) y_speed = -10.0f;

                    // 控制电机（使用basic.c的方向）
                    Step_Motor_Set_Speed_my(-x_speed, -y_speed);

                    // 调试信息
                    my_printf(&huart1, "移动到右上角: 目标(%d,%d) 当前(%d,%d) 距离%d\r\n",
                              (int)border_ctrl.target_point.x, (int)border_ctrl.target_point.y,
                              latest_red_laser_coord.x, latest_red_laser_coord.y, (int)distance);
                } else {
                    my_printf(&huart1, "错误：目标点未设置\r\n");
                    border_ctrl.state = BORDER_TRACE_ERROR;
                }
            }
            break;

        case BORDER_TRACE_CORRECTING:
            // 纠正模式：将激光点拉回边框区域
            border_ctrl.correction_attempts++;

            if (border_ctrl.correction_attempts > 50) {  // 最多尝试50次
                my_printf(&huart1, "纠正失败，停止循迹\r\n");
                stop_border_trace();
                break;
            }

            // 找到最近的边框路径点（简化版本）
            Point correction_target = border_ctrl.target_point;  // 使用当前目标点

            // 声明变量
            int dx_corr = correction_target.x - current_laser_pos.x;
            int dy_corr = correction_target.y - current_laser_pos.y;

            // 使用更大的增益进行纠正
            float x_speed_corr = border_ctrl.kp_border * 2.0f * dx_corr;
            float y_speed_corr = border_ctrl.kp_border * 2.0f * dy_corr;

            // 限制速度
            x_speed_corr = border_clamp(x_speed_corr, -12.0f, 12.0f);
            y_speed_corr = border_clamp(y_speed_corr, -12.0f, 12.0f);

            Step_Motor_Set_Speed_my(x_speed_corr, -y_speed_corr);

            // 检查是否已回到边框内（简化版本）
            float dist_to_target = sqrt(dx_corr*dx_corr + dy_corr*dy_corr);
            if (dist_to_target <= 10.0f) {
                border_ctrl.state = BORDER_TRACE_MOVING;
                border_ctrl.state_start_time = current_time;
                my_printf(&huart1, "纠正成功，恢复循迹\r\n");
            }

            my_printf(&huart1, "纠正中: 尝试%d 目标(%d,%d) 速度(%d,%d)\r\n",
                      border_ctrl.correction_attempts,
                      (int)correction_target.x, (int)correction_target.y, (int)x_speed_corr, (int)(-y_speed_corr));
            break;

        default:
            break;
    }

    // 保存当前激光位置
    border_ctrl.last_laser_pos = current_laser_pos;
}

/**
 * @brief 获取边框循迹状态信息
 */
void get_border_trace_status(char* info_buffer, int buffer_size)
{
    if (!info_buffer || buffer_size <= 0) return;

    const char* state_names[] = {
        "IDLE", "INIT", "MOVING", "CORRECTING", "COMPLETED", "ERROR"
    };

    snprintf(info_buffer, buffer_size,
             "State:%s Active:%d Path:%d/%d Pos:(%.1f,%.1f) Target:(%.1f,%.1f)",
             state_names[border_ctrl.state],
             border_ctrl.is_active,
             border_ctrl.path.current_index,
             border_ctrl.path.path_length,
             border_ctrl.last_laser_pos.x,
             border_ctrl.last_laser_pos.y,
             border_ctrl.target_point.x,
             border_ctrl.target_point.y);
}

// --- 内部函数实现 ---

/**
 * @brief 计算内边界顶点（假设边框宽度均匀）
 */
static bool calculate_inner_vertices(void)
{
    // 计算外边界的中心点
    Point center = {0, 0};
    for (int i = 0; i < 4; i++) {
        center.x += border_ctrl.region.outer_vertices[i].x;
        center.y += border_ctrl.region.outer_vertices[i].y;
    }
    center.x /= 4.0f;
    center.y /= 4.0f;

    // 估算边框宽度（使用外边界到中心距离的一定比例）
    float avg_distance = 0.0f;
    for (int i = 0; i < 4; i++) {
        avg_distance += border_distance(border_ctrl.region.outer_vertices[i], center);
    }
    avg_distance /= 4.0f;

    // 边框宽度设为平均距离的30%
    border_ctrl.region.border_width = avg_distance * 0.3f;

    // 计算内边界顶点（向中心收缩）
    for (int i = 0; i < 4; i++) {
        Point outer = border_ctrl.region.outer_vertices[i];

        // 计算从外顶点到中心的方向向量
        float dx = center.x - outer.x;
        float dy = center.y - outer.y;
        float dist = sqrtf(dx*dx + dy*dy);

        if (dist > 0) {
            // 单位化方向向量
            dx /= dist;
            dy /= dist;

            // 向中心移动边框宽度的距离
            border_ctrl.region.inner_vertices[i].x = outer.x + dx * border_ctrl.region.border_width;
            border_ctrl.region.inner_vertices[i].y = outer.y + dy * border_ctrl.region.border_width;
        } else {
            border_ctrl.region.inner_vertices[i] = outer;
        }
    }

    return true;
}

/**
 * @brief 找到距离当前位置最近的路径点索引
 */
static int find_closest_path_point(Point current_pos)
{
    if (border_ctrl.path.path_length == 0) return 0;

    float min_dist = border_distance(current_pos, border_ctrl.path.path_points[0]);
    int closest_idx = 0;

    for (int i = 1; i < border_ctrl.path.path_length; i++) {
        float dist = border_distance(current_pos, border_ctrl.path.path_points[i]);
        if (dist < min_dist) {
            min_dist = dist;
            closest_idx = i;
        }
    }

    return closest_idx;
}

/**
 * @brief 计算外边界点和内边界点的中心点
 */
static Point calculate_border_center_point(Point outer, Point inner)
{
    Point center;
    center.x = (outer.x + inner.x) / 2.0f;
    center.y = (outer.y + inner.y) / 2.0f;
    return center;
}

/**
 * @brief 使用射线法判断点是否在多边形内部
 */
static bool is_point_inside_polygon(Point point, Point* vertices, int vertex_count)
{
    if (vertex_count < 3) return false;

    bool inside = false;
    int j = vertex_count - 1;

    for (int i = 0; i < vertex_count; i++) {
        if (((vertices[i].y > point.y) != (vertices[j].y > point.y)) &&
            (point.x < (vertices[j].x - vertices[i].x) * (point.y - vertices[i].y) /
             (vertices[j].y - vertices[i].y) + vertices[i].x)) {
            inside = !inside;
        }
        j = i;
    }

    return inside;
}

/**
 * @brief 重置边框控制器状态
 */
static void reset_border_controller(void)
{
    border_ctrl.state = BORDER_TRACE_IDLE;
    border_ctrl.is_active = false;
    border_ctrl.emergency_stop = false;
    border_ctrl.correction_attempts = 0;
    border_ctrl.path.current_index = 0;
    border_ctrl.last_update_time = 0;

    // 重置路径序列
    border_ctrl.sequence_length = 0;
    border_ctrl.current_sequence_index = 0;
    border_ctrl.use_sequence_mode = false;
    memset(border_ctrl.path_sequence, 0, sizeof(border_ctrl.path_sequence));

    // 清空路径数据
    memset(&border_ctrl.path, 0, sizeof(BorderPath));
    memset(&border_ctrl.region, 0, sizeof(BorderRegion));
}

// --- 安全路径检查函数 ---

/**
 * @brief 检查从起点到终点的直线路径是否安全（在胶带区域内）
 * @param start 起点坐标
 * @param end 终点坐标
 * @return true表示路径安全，false表示路径不安全
 */
bool is_direct_path_safe(Point start, Point end)
{
    // 检查边框区域是否有效
    if (!border_ctrl.region.is_valid) {
        my_printf(&huart1, "警告：边框区域无效，无法进行路径安全检查\r\n");
        return false;
    }

    // 生成路径上的检查点（线性插值）
    const int check_points = 8; // 在路径上检查8个点

    for (int i = 0; i <= check_points; i++) {
        float t = (float)i / (float)check_points; // 插值参数 0.0 到 1.0

        // 计算插值点
        Point check_point;
        check_point.x = start.x + t * (end.x - start.x);
        check_point.y = start.y + t * (end.y - start.y);

        // 检查该点是否在胶带区域内
        if (!is_point_in_border_region(check_point)) {
            return false;
        }
    }

    return true;
}

/**
 * @brief 生成从起点到终点的安全路径点
 * @param start 起点坐标
 * @param end 终点坐标
 * @param path_array 输出路径点数组
 * @return 生成的路径点数量
 */
int generate_safe_path_points(Point start, Point end, Point* path_array)
{
    if (!path_array) {
        return 0;
    }

    // 首先检查直线路径是否安全
    if (is_direct_path_safe(start, end)) {
        // 直线路径安全，只需要终点
        path_array[0] = end;
        return 1;
    }

    // 简化策略：生成沿上边缘的路径点
    // 找到矩形的上边缘Y坐标（最小Y值）
    float min_y = border_ctrl.region.outer_vertices[0].y;
    for (int i = 1; i < 4; i++) {
        if (border_ctrl.region.outer_vertices[i].y < min_y) {
            min_y = border_ctrl.region.outer_vertices[i].y;
        }
    }

    // 生成沿上边缘的中间路径点
    int path_count = 0;

    // 添加中间点（沿上边缘移动）
    float mid_x = (start.x + end.x) / 2.0f;
    Point mid_point = {mid_x, min_y + 10.0f}; // 稍微向下偏移确保在胶带内

    // 检查中间点是否安全
    if (is_point_in_border_region(mid_point)) {
        path_array[path_count++] = mid_point;
    }

    // 添加终点
    path_array[path_count++] = end;

    return path_count;
}

/**
 * @brief 初始化路径序列
 * @param points 路径点数组
 * @param count 路径点数量
 */
void init_path_sequence(Point* points, int count)
{
    if (!points || count <= 0 || count > 10) {
        my_printf(&huart1, "错误：路径序列参数无效\r\n");
        border_ctrl.use_sequence_mode = false;
        return;
    }

    // 复制路径点到序列数组
    for (int i = 0; i < count; i++) {
        border_ctrl.path_sequence[i] = points[i];
    }

    // 设置序列参数
    border_ctrl.sequence_length = count;
    border_ctrl.current_sequence_index = 0;
    border_ctrl.use_sequence_mode = true;

    // 序列初始化完成，无需详细输出
}


