#include "basic.h"
#include "pi_bsp.h"
#include "step_motor_bsp.h"
#include "../app/Emm_V5.h"

// 外部变量声明
extern UART_HandleTypeDef huart1;

// --- 循迹功能全局变量 ---
static TraceController trace_ctrl = {0};
static bool is_tracing = false;

// --- 内部函数声明 ---
static int find_left_top_corner(void);
static void init_trace_vertices(void);
static bool move_to_point(Point target, float tolerance);
static bool points_equal(Point p1, Point p2);
static Point find_nearest_vertex(Point current_pos);
static int get_perimeter_path(Point start_vertex, Point end_vertex, Point path_waypoints[], int max_waypoints);
static void handle_perimeter_movement(void);

// --- 主要功能实现 ---

/**
 * @brief 激光点移动到矩形左上方顶点
 *
 * 不依赖PID追踪系统，直接使用电机控制移动到目标位置。
 * 这样可以避免自动追踪功能的干扰，便于测试新功能。
 */
void move_laser_to_rectangle_corner(void)
{
    my_printf(&huart1, "\r\n=== 激光点移动到矩形左上方顶点 ===\r\n");

    // 检查矩形坐标是否有效
    if (!latest_rect_coord.isValid) {
        my_printf(&huart1, "错误：未检测到有效的矩形坐标\r\n");
        my_printf(&huart1, "请确保摄像头已检测到矩形并发送坐标数据\r\n");
        return;
    }

    // 显示当前矩形的四个角点
    my_printf(&huart1, "检测到矩形四个角点:\r\n");
    for (int i = 0; i < 4; i++) {
        my_printf(&huart1, "  角点%d: (%d, %d)\r\n", i,
                  latest_rect_coord.x[i], latest_rect_coord.y[i]);
    }

    // 找到左上角点
    int left_top_index = find_left_top_corner();

    my_printf(&huart1, "左上角点为角点%d: (%d, %d)\r\n", left_top_index,
              latest_rect_coord.x[left_top_index], latest_rect_coord.y[left_top_index]);

    // 显示当前红色激光位置（如果有效）
    if (latest_red_laser_coord.isValid) {
        my_printf(&huart1, "当前红色激光位置: (%d, %d)\r\n",
                  latest_red_laser_coord.x, latest_red_laser_coord.y);

        // 计算距离
        int dx = latest_rect_coord.x[left_top_index] - latest_red_laser_coord.x;
        int dy = latest_rect_coord.y[left_top_index] - latest_red_laser_coord.y;
        float distance = sqrt(dx*dx + dy*dy);
        my_printf(&huart1, "距离目标: %.1f 像素\r\n", distance);
    } else {
        my_printf(&huart1, "注意：当前未检测到红色激光点\r\n");
    }

    // 检查红色激光坐标是否有效
    if (!latest_red_laser_coord.isValid) {
        my_printf(&huart1, "错误：未检测到红色激光点，无法移动\r\n");
        return;
    }

    // 计算像素误差
    int dx = latest_rect_coord.x[left_top_index] - latest_red_laser_coord.x;
    int dy = latest_rect_coord.y[left_top_index] - latest_red_laser_coord.y;

    // 使用校准后的比例系数（基于实际测试数据调整）
    float kp = 0.022f;  // 从0.02增加到0.1，提高5倍速度
    float x_speed = kp * dx;  // X轴速度
    float y_speed = kp * dy;  // Y轴速度

    // 限制速度在电机最大范围内（±3 RPM）
    if (x_speed > 10.0f) x_speed = 10.0f;
    if (x_speed < -10.0f) x_speed = -10.0f;
    if (y_speed > 10.0f) y_speed = 10.0f;
    if (y_speed < -10.0f) y_speed = -10.0f;

    // 显示计算结果
    my_printf(&huart1, "计算移动参数: X速度=%.2f RPM, Y速度=%.2f RPM\r\n", x_speed, y_speed);

    // 计算移动时间（基于实际测试数据校准：每像素50ms）
    float max_error = (abs(dx) > abs(dy)) ? abs(dx) : abs(dy);
    int move_time_ms = (int)(max_error * 10);  // 从10增加到50，提高5倍时间
    if (move_time_ms > 3000) move_time_ms = 3000;  // 最大5秒
    if (move_time_ms < 500) move_time_ms = 500;  // 最小1秒

    my_printf(&huart1, "开始移动，预计时间: %d ms\r\n", move_time_ms);

    // 执行电机控制（X轴取负号，基于PID系统分析）
    Step_Motor_Set_Speed_my(x_speed, -y_speed);

    // 移动指定时间
    HAL_Delay(move_time_ms);

    // 停止电机
    Step_Motor_Stop();

    my_printf(&huart1, "电机移动完成\r\n");
    my_printf(&huart1, "=== 激光点移动任务完成 ===\r\n\r\n");
}

// --- 循迹功能实现 ---

/**
 * @brief 获取当前激光位置
 */
Point get_current_laser_position(void)
{
    Point pos = {0, 0};
    if (latest_red_laser_coord.isValid) {
        pos.x = latest_red_laser_coord.x;
        pos.y = latest_red_laser_coord.y;
    }
    return pos;
}

/**
 * @brief 计算两点间距离
 */
float calculate_distance(Point p1, Point p2)
{
    int dx = p2.x - p1.x;
    int dy = p2.y - p1.y;
    return sqrt(dx*dx + dy*dy);
}

/**
 * @brief 通用点到点移动函数
 * @param target 目标点坐标
 * @param tolerance 到达容差（像素）
 * @return true表示已到达目标，false表示还在移动中
 */
static bool move_to_point(Point target, float tolerance)
{
    // 检查红色激光坐标是否有效
    if (!latest_red_laser_coord.isValid) {
        return false; // 数据无效，继续等待
    }

    // 计算误差
    int dx = target.x - latest_red_laser_coord.x;
    int dy = target.y - latest_red_laser_coord.y;
    float distance = sqrt(dx*dx + dy*dy);

    // 检查是否到达目标
    if (distance <= tolerance) {
        Step_Motor_Stop();
        return true; // 已到达
    }

    // 计算速度（使用现有参数）
    float kp = 0.022f;
    float x_speed = kp * dx;
    float y_speed = kp * dy;

    // 限制速度在电机最大范围内
    if (x_speed > 10.0f) x_speed = 10.0f;
    if (x_speed < -10.0f) x_speed = -10.0f;
    if (y_speed > 10.0f) y_speed = 10.0f;
    if (y_speed < -10.0f) y_speed = -10.0f;

    // 添加调试信息
    my_printf(&huart1, "移动调试: 目标(%d,%d) 当前(%d,%d) 误差(%d,%d) 速度(%.2f,%.2f)\r\n",
              target.x, target.y,
              latest_red_laser_coord.x, latest_red_laser_coord.y,
              dx, dy, -x_speed, -y_speed);

    // 控制电机移动（调整Y轴方向映射）
    Step_Motor_Set_Speed_my(-x_speed, -y_speed);

    return false; // 还在移动中
}

// --- 沿边框移动辅助函数 ---

/**
 * @brief 判断两个点是否相等（在容差范围内）
 */
static bool points_equal(Point p1, Point p2)
{
    const float tolerance = 20.0f; // 20像素容差
    return calculate_distance(p1, p2) < tolerance;
}

/**
 * @brief 找到距离当前位置最近的矩形顶点
 */
static Point find_nearest_vertex(Point current_pos)
{
    float min_distance = 999999.0f;
    int nearest_index = 0;

    for (int i = 0; i < 4; i++) {
        float distance = calculate_distance(current_pos, trace_ctrl.vertices[i]);
        if (distance < min_distance) {
            min_distance = distance;
            nearest_index = i;
        }
    }

    my_printf(&huart1, "最近顶点：顶点%d (%d, %d)，距离：%.1f像素\r\n",
              nearest_index, trace_ctrl.vertices[nearest_index].x,
              trace_ctrl.vertices[nearest_index].y, min_distance);

    return trace_ctrl.vertices[nearest_index];
}

/**
 * @brief 查找左上角顶点索引
 */
static int find_left_top_corner(void)
{
    int left_top_index = 0;
    for (int i = 1; i < 4; i++) {
        if (latest_rect_coord.y[i] < latest_rect_coord.y[left_top_index] ||
            (latest_rect_coord.y[i] == latest_rect_coord.y[left_top_index] &&
             latest_rect_coord.x[i] < latest_rect_coord.x[left_top_index])) {
            left_top_index = i;
        }
    }
    return left_top_index;
}

/**
 * @brief 初始化循迹顶点数组
 */
static void init_trace_vertices(void)
{
    if (!latest_rect_coord.isValid) {
        my_printf(&huart1, "错误：未检测到有效矩形，无法初始化循迹\r\n");
        return;
    }

    // 找到左上角顶点作为起始点
    int left_top_index = find_left_top_corner();

    // 显示检测到的矩形四个角点
    my_printf(&huart1, "检测到矩形四个角点:\r\n");
    for (int i = 0; i < 4; i++) {
        my_printf(&huart1, "  角点%d: (%d, %d)\r\n", i,
                  latest_rect_coord.x[i], latest_rect_coord.y[i]);
    }

    // 按顺时针顺序设置顶点：左上 → 右上 → 右下 → 左下 → 左上
    // 基于左上角顶点，确定其他顶点的顺序
    trace_ctrl.vertices[0] = (Point){latest_rect_coord.x[left_top_index], latest_rect_coord.y[left_top_index]};

    // 找到其他顶点（简化处理：按角点索引顺序）
    int next_index = (left_top_index + 1) % 4;
    trace_ctrl.vertices[1] = (Point){latest_rect_coord.x[next_index], latest_rect_coord.y[next_index]};

    next_index = (left_top_index + 2) % 4;
    trace_ctrl.vertices[2] = (Point){latest_rect_coord.x[next_index], latest_rect_coord.y[next_index]};

    next_index = (left_top_index + 3) % 4;
    trace_ctrl.vertices[3] = (Point){latest_rect_coord.x[next_index], latest_rect_coord.y[next_index]};

    // 最后回到起始点
    trace_ctrl.vertices[4] = trace_ctrl.vertices[0];

    trace_ctrl.total_vertices = 5;

    // 显示循迹路径
    my_printf(&huart1, "循迹路径规划:\r\n");
    for (int i = 0; i < trace_ctrl.total_vertices; i++) {
        my_printf(&huart1, "  顶点%d: (%d, %d)\r\n", i,
                  trace_ctrl.vertices[i].x, trace_ctrl.vertices[i].y);
    }
}

/**
 * @brief 计算沿矩形边框的路径
 * @param start_vertex 起始顶点
 * @param end_vertex 目标顶点
 * @param path_waypoints 用于存储路径点的数组
 * @param max_waypoints 路径数组的最大容量
 * @return 路径长度，-1表示错误，0表示无需移动
 */
static int get_perimeter_path(Point start_vertex, Point end_vertex, Point path_waypoints[], int max_waypoints)
{
    // 矩形顶点按顺时针顺序：左上、右上、右下、左下
    Point ordered_vertices[4] = {
        trace_ctrl.vertices[0], // 左上
        trace_ctrl.vertices[1], // 右上
        trace_ctrl.vertices[2], // 右下
        trace_ctrl.vertices[3]  // 左下
    };

    // 找到起始和结束顶点的索引
    int start_idx = -1, end_idx = -1;
    for (int i = 0; i < 4; i++) {
        if (points_equal(start_vertex, ordered_vertices[i])) start_idx = i;
        if (points_equal(end_vertex, ordered_vertices[i])) end_idx = i;
    }

    if (start_idx == -1 || end_idx == -1) {
        my_printf(&huart1, "错误：起始或目标顶点不在矩形上\r\n");
        return -1;
    }

    if (start_idx == end_idx) {
        my_printf(&huart1, "起始点与目标点相同，无需移动\r\n");
        return 0;
    }

    // 计算顺时针路径
    Point path_cw[4];
    int len_cw = 0;
    int current_idx = start_idx;
    while (current_idx != end_idx && len_cw < max_waypoints) {
        current_idx = (current_idx + 1) % 4;
        path_cw[len_cw++] = ordered_vertices[current_idx];
    }

    // 计算逆时针路径
    Point path_ccw[4];
    int len_ccw = 0;
    current_idx = start_idx;
    while (current_idx != end_idx && len_ccw < max_waypoints) {
        current_idx = (current_idx - 1 + 4) % 4;
        path_ccw[len_ccw++] = ordered_vertices[current_idx];
    }

    // 选择较短的路径
    if (len_cw <= len_ccw) {
        my_printf(&huart1, "选择顺时针路径（长度：%d）\r\n", len_cw);
        for (int i = 0; i < len_cw; i++) {
            path_waypoints[i] = path_cw[i];
        }
        return len_cw;
    } else {
        my_printf(&huart1, "选择逆时针路径（长度：%d）\r\n", len_ccw);
        for (int i = 0; i < len_ccw; i++) {
            path_waypoints[i] = path_ccw[i];
        }
        return len_ccw;
    }
}

/**
 * @brief 处理沿边框移动的逻辑
 */
static void handle_perimeter_movement(void)
{
    if (!trace_ctrl.use_perimeter_mode ||
        trace_ctrl.current_waypoint >= trace_ctrl.waypoint_count) {
        return;
    }

    // 获取当前要移动到的路径点
    Point current_waypoint = trace_ctrl.waypoints[trace_ctrl.current_waypoint];

    // 使用现有的移动函数移动到路径点
    if (move_to_point(current_waypoint, 15.0f)) {
        my_printf(&huart1, "到达路径点%d: (%d, %d)\r\n",
                  trace_ctrl.current_waypoint + 1,
                  current_waypoint.x, current_waypoint.y);
        trace_ctrl.state = TRACE_AT_WAYPOINT;
    }
}

/**
 * @brief 循迹处理函数（非阻塞状态机）
 */
void trace_proc(void)
{
    if (!is_tracing) return;

    uint32_t current_time = HAL_GetTick();

    switch (trace_ctrl.state) {
        case TRACE_IDLE:
            // 开始移动到当前目标点
            if (trace_ctrl.current_target < trace_ctrl.total_vertices) {
                Point target = trace_ctrl.vertices[trace_ctrl.current_target];
                my_printf(&huart1, "开始移动到顶点%d: (%d, %d)\r\n",
                         trace_ctrl.current_target, target.x, target.y);

                if (trace_ctrl.current_target != 4) {
                    // 沿边框移动模式（最后一步除外）
                    Point current_pos = get_current_laser_position();
                    Point start_vertex = find_nearest_vertex(current_pos);

                    Point path_waypoints[4];
                    int path_len = get_perimeter_path(start_vertex, target, path_waypoints, 4);

                    if (path_len > 0) {
                        // 设置路径
                        trace_ctrl.waypoint_count = path_len;
                        trace_ctrl.current_waypoint = 0;
                        for (int i = 0; i < path_len; i++) {
                            trace_ctrl.waypoints[i] = path_waypoints[i];
                        }
                        trace_ctrl.use_perimeter_mode = true;

                        my_printf(&huart1, "沿边框路径规划完成，共%d个路径点：\r\n", path_len);
                        for (int i = 0; i < path_len; i++) {
                            my_printf(&huart1, "  路径点%d: (%d, %d)\r\n", i+1,
                                      path_waypoints[i].x, path_waypoints[i].y);
                        }
                    } else if (path_len == 0) {
                        // 已在目标点
                        trace_ctrl.state = TRACE_AT_TARGET;
                        trace_ctrl.state_start_time = current_time;
                        break;
                    } else {
                        // 路径规划失败，使用直线模式
                        my_printf(&huart1, "沿边框路径规划失败，使用直线模式\r\n");
                        trace_ctrl.use_perimeter_mode = false;
                    }
                } else {
                    // 直线移动模式（或最后一步回到初始位置）
                    trace_ctrl.use_perimeter_mode = false;
                    if (trace_ctrl.current_target == 4) {
                        my_printf(&huart1, "最后一步：直线移动回到初始位置\r\n");
                    }
                }

                trace_ctrl.state = TRACE_MOVING;
                trace_ctrl.state_start_time = current_time;
            } else {
                trace_ctrl.state = TRACE_COMPLETED;
            }
            break;

        case TRACE_MOVING:
            // 持续移动到目标点
            if (trace_ctrl.use_perimeter_mode) {
                // 沿边框移动模式
                handle_perimeter_movement();
            } else {
                // 直线移动模式
                Point target = trace_ctrl.vertices[trace_ctrl.current_target];
                if (move_to_point(target, 15.0f)) { // 15像素容差
                    my_printf(&huart1, "到达顶点%d\r\n", trace_ctrl.current_target);
                    trace_ctrl.state = TRACE_AT_TARGET;
                    trace_ctrl.state_start_time = current_time;
                }
            }

            // 超时保护（15秒，沿边框移动需要更多时间）
            if (current_time - trace_ctrl.state_start_time > 15000) {
                my_printf(&huart1, "移动超时，跳过顶点%d\r\n", trace_ctrl.current_target);
                trace_ctrl.state = TRACE_AT_TARGET;
                trace_ctrl.state_start_time = current_time;
            }
            break;

        case TRACE_AT_WAYPOINT:
            // 到达路径点，准备移动到下一个路径点
            trace_ctrl.current_waypoint++;
            if (trace_ctrl.current_waypoint >= trace_ctrl.waypoint_count) {
                // 所有路径点都完成，到达目标顶点
                my_printf(&huart1, "沿边框路径完成，到达目标顶点%d\r\n", trace_ctrl.current_target);
                trace_ctrl.state = TRACE_AT_TARGET;
                trace_ctrl.use_perimeter_mode = false;
            } else {
                // 继续移动到下一个路径点
                Point next_waypoint = trace_ctrl.waypoints[trace_ctrl.current_waypoint];
                my_printf(&huart1, "继续移动到路径点%d: (%d, %d)\r\n",
                          trace_ctrl.current_waypoint + 1, next_waypoint.x, next_waypoint.y);
                trace_ctrl.state = TRACE_MOVING;
            }
            trace_ctrl.state_start_time = current_time;
            break;

        case TRACE_AT_TARGET:
            // 在目标点停留一段时间，然后移动到下一个点
            if (current_time - trace_ctrl.state_start_time > 500) { // 停留500ms
                trace_ctrl.current_target++;
                trace_ctrl.state = TRACE_IDLE;
            }
            break;

        case TRACE_COMPLETED:
            my_printf(&huart1, "=== 矩形循迹完成 ===\r\n");
            is_tracing = false;
            Step_Motor_Stop();
            break;
    }
}



/**
 * @brief 停止矩形循迹
 */
void stop_rectangle_trace(void)
{
    if (!is_tracing) {
        my_printf(&huart1, "当前没有进行循迹\r\n");
        return;
    }

    my_printf(&huart1, "停止矩形循迹\r\n");
    is_tracing = false;
    Step_Motor_Stop();

    // 清理状态
    trace_ctrl.current_target = 0;
    trace_ctrl.state = TRACE_IDLE;
}

/**
 * @brief 一键循迹功能（移动到起点 + 开始循迹）
 */
void move_to_corner_and_start_trace(void)
{
    my_printf(&huart1, "\r\n=== 一键循迹功能启动 ===\r\n");

    // 记录初始位置（用于最后的复位）
    Point initial_pos = get_current_laser_position();
    if (!latest_red_laser_coord.isValid) {
        my_printf(&huart1, "警告：激光位置数据无效，使用默认初始位置\r\n");
        initial_pos = (Point){100, 100}; // 默认位置
    }
    trace_ctrl.initial_position = initial_pos;
    my_printf(&huart1, "记录初始位置: (%d, %d)\r\n", initial_pos.x, initial_pos.y);

    // 检查矩形坐标是否有效
    if (!latest_rect_coord.isValid) {
        my_printf(&huart1, "错误：未检测到有效矩形，无法执行循迹\r\n");
        return;
    }

    // 如果已经在循迹中，先停止
    if (is_tracing) {
        my_printf(&huart1, "停止当前循迹...\r\n");
        stop_rectangle_trace();
        HAL_Delay(500); // 等待停止完成
    }

    // 第一步：移动到矩形左上角
    my_printf(&huart1, "步骤1：移动到矩形左上角...\r\n");
    move_laser_to_rectangle_corner();

    // 等待一小段时间让系统稳定
    my_printf(&huart1, "等待系统稳定...\r\n");
    HAL_Delay(1000);

    // 第二步：开始优化循迹（沿边框模式，从右上角开始）
    my_printf(&huart1, "步骤2：开始沿边框循迹（跳过当前位置）...\r\n");

    // 初始化循迹控制器（内联优化版本）
    if (is_tracing) {
        my_printf(&huart1, "循迹已在进行中，请先停止当前循迹\r\n");
        return;
    }

    my_printf(&huart1, "初始化循迹路径...\r\n");
    init_trace_vertices();

    // 修改最后一个顶点为初始位置（实现复位功能）
    trace_ctrl.vertices[4] = trace_ctrl.initial_position;
    my_printf(&huart1, "修改最终目标为初始位置: (%d, %d)\r\n",
              trace_ctrl.initial_position.x, trace_ctrl.initial_position.y);

    // 优化：由于已经在左上角，直接从右上角开始循迹
    trace_ctrl.current_target = 1;  // 跳过顶点0（左上角），从顶点1（右上角）开始
    trace_ctrl.state = TRACE_IDLE;
    trace_ctrl.state_start_time = HAL_GetTick();
    is_tracing = true;

    my_printf(&huart1, "沿边框循迹路径：右上角→右下角→左下角→左上角→初始位置（复位）\r\n");
    my_printf(&huart1, "=== 一键沿边框循迹功能启动完成 ===\r\n");
    my_printf(&huart1, "激光点将沿着矩形边框自动完成循迹并复位，请观察移动过程\r\n\r\n");
}
