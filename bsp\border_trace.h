#ifndef __BORDER_TRACE_H__
#define __BORDER_TRACE_H__

#include "bsp_system.h"
#include "basic.h"
#include <math.h>

// --- 配置参数 ---
#define MAX_BORDER_PATH_POINTS  200     // 最大边框路径点数
#define BORDER_PATH_SPACING     5.0f    // 路径点间距（像素）
#define BORDER_LOOKAHEAD_DIST   15.0f   // 前瞻距离（像素）
#define BORDER_SAFETY_MARGIN    8.0f    // 安全边距（像素）
#define BORDER_ARRIVAL_TOLERANCE 3.0f   // 到达容差（像素）

// --- 边框循迹状态枚举 ---
typedef enum {
    BORDER_TRACE_IDLE,           // 空闲状态
    BORDER_TRACE_INIT,           // 初始化路径
    BORDER_TRACE_MOVING,         // 正在移动
    BORDER_TRACE_CORRECTING,     // 边界纠正
    BORDER_TRACE_COMPLETED,      // 循迹完成
    BORDER_TRACE_ERROR           // 错误状态
} BorderTraceState;

// --- 边框区域结构体 ---
typedef struct {
    Point outer_vertices[4];     // 外边界顶点（来自摄像头rect数据）
    Point inner_vertices[4];     // 内边界顶点（计算得出）
    bool is_valid;               // 数据是否有效
    float border_width;          // 边框宽度（像素）
} BorderRegion;

// --- 边框路径结构体 ---
typedef struct {
    Point path_points[MAX_BORDER_PATH_POINTS];  // 路径点数组
    int path_length;                            // 路径长度
    int current_index;                          // 当前路径点索引
    float total_distance;                       // 总路径长度
    bool is_generated;                          // 路径是否已生成
} BorderPath;

// --- 边框循迹控制器 ---
typedef struct {
    BorderRegion region;                // 边框区域
    BorderPath path;                    // 边框路径
    BorderTraceState state;             // 当前状态
    uint32_t state_start_time;          // 状态开始时间
    Point target_point;                 // 当前目标点
    Point last_laser_pos;               // 上次激光位置
    bool is_active;                     // 是否激活
    uint32_t last_update_time;          // 上次更新时间
    
    // 控制参数
    float kp_border;                    // 边框模式PID参数
    float lookahead_distance;           // 前瞻距离
    int correction_attempts;            // 纠正尝试次数
    bool emergency_stop;                // 紧急停止标志

    // 路径序列支持
    Point path_sequence[10];            // 路径序列点数组
    int sequence_length;                // 序列长度
    int current_sequence_index;         // 当前序列索引
    bool use_sequence_mode;             // 是否使用序列模式
} BorderTraceController;

// --- 全局变量声明 ---
extern BorderTraceController border_ctrl;

// --- 核心函数声明 ---

/**
 * @brief 初始化边框循迹控制器
 */
void border_trace_init(void);

/**
 * @brief 从矩形数据生成边框区域
 * @param rect_data 矩形坐标数据（来自摄像头）
 * @return true表示成功，false表示失败
 */
bool generate_border_region(RectCoord_t* rect_data);

/**
 * @brief 生成边框中心线路径
 * @return true表示成功，false表示失败
 */
bool generate_border_path(void);

/**
 * @brief 检查点是否在边框区域内
 * @param point 要检查的点
 * @return true表示在边框内，false表示不在
 */
bool is_point_in_border_region(Point point);

/**
 * @brief 找到路径上的前瞻目标点
 * @param current_pos 当前位置
 * @return 前瞻目标点
 */
Point find_border_lookahead_target(Point current_pos);

/**
 * @brief 计算到边框边界的最短距离
 * @param point 要检查的点
 * @return 到边界的距离（负值表示在边框外）
 */
float distance_to_border_boundary(Point point);

/**
 * @brief 边框循迹主处理函数（状态机）
 * 需要在调度器中定期调用
 */
void border_trace_proc(void);

/**
 * @brief 启动边框循迹
 * @return true表示启动成功，false表示失败
 */
bool start_border_trace(void);

/**
 * @brief 简单的移动到左上角功能（测试用）
 * @return true表示启动成功，false表示失败
 */
bool start_move_to_left_top_corner(void);

/**
 * @brief 简单的移动到右上角功能（测试用）
 * @return true表示启动成功，false表示失败
 */
bool start_move_to_right_top_corner(void);

/**
 * @brief 停止边框循迹
 */
void stop_border_trace(void);

/**
 * @brief 紧急停止边框循迹
 */
void emergency_stop_border_trace(void);

/**
 * @brief 获取边框循迹状态信息
 * @param info_buffer 信息缓冲区
 * @param buffer_size 缓冲区大小
 */
void get_border_trace_status(char* info_buffer, int buffer_size);

/**
 * @brief 检查从起点到终点的直线路径是否安全（在胶带区域内）
 * @param start 起点坐标
 * @param end 终点坐标
 * @return true表示路径安全，false表示路径不安全
 */
bool is_direct_path_safe(Point start, Point end);

/**
 * @brief 生成从起点到终点的安全路径点
 * @param start 起点坐标
 * @param end 终点坐标
 * @param path_array 输出路径点数组
 * @return 生成的路径点数量
 */
int generate_safe_path_points(Point start, Point end, Point* path_array);

/**
 * @brief 初始化路径序列
 * @param points 路径点数组
 * @param count 路径点数量
 */
void init_path_sequence(Point* points, int count);

// --- 辅助函数 ---

/**
 * @brief 计算两点间距离
 */
static inline float border_distance(Point p1, Point p2) {
    float dx = p2.x - p1.x;
    float dy = p2.y - p1.y;
    return sqrtf(dx*dx + dy*dy);
}

/**
 * @brief 限制值在指定范围内
 */
static inline float border_clamp(float value, float min_val, float max_val) {
    if (value < min_val) return min_val;
    if (value > max_val) return max_val;
    return value;
}

#endif // __BORDER_TRACE_H__
