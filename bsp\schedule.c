#include "schedule.h"
#include "border_trace.h"

typedef struct {
	void (*task_func)(void);
	uint32_t rate_ms;
	uint32_t last_run;
}schedule_task_t;

uint8_t task_num;

static schedule_task_t schedule_task[] = {
	// 激光追踪功能 - 已禁用用于测试新功能
	{uart_proc,1,0},    // UART通信处理 - 保留用于接收激光数据
	// {pi_proc,20,0},     // 激光追踪处理 - 已禁用，避免自动追踪影响测试

	// 按键处理功能
	{key_proc,10,0},    // 按键处理任务 - 10ms间隔

	// 循迹功能
	{trace_proc,50,0},   // 循迹处理任务 - 50ms间隔

	// 边框循迹功能
	{border_trace_proc,30,0}  // 边框循迹处理任务 - 30ms间隔，更高频率确保精确控制

	// Basic功能 - 已注释掉
//	{oled_proc,100,0},   // OLED显示功能
//	{motor_proc,20,0},   // 电机处理功能
//	{encoder_proc,20,0}, // 编码器处理功能
//	{gray_proc,20,0}     // 灰度传感器处理功能

};

/**
 * @brief 调度器初始化函数
 * 计算任务数组的元素个数，并将结果存储在 task_num 中
 */
void schedule_init(void)
{
	task_num = sizeof(schedule_task) / sizeof(schedule_task_t);
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经超过任务的执行周期，则执行该任务并更新上次运行时间
 */
void schedule_run(void)
{
    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前的系统时间（毫秒）
        uint32_t now_time = HAL_GetTick();

        // 检查当前时间是否达到任务的执行时间
        if (now_time >= schedule_task[i].rate_ms + schedule_task[i].last_run)
        {
            // 更新任务的上次运行时间为当前时间
            schedule_task[i].last_run = now_time;

            // 执行任务函数
            schedule_task[i].task_func();
        }
    }
}
