#include "key_bsp.h"
#include "basic.h"
#include "border_trace.h"

uint8_t key_val = 0;
uint8_t key_old = 0;
uint8_t key_down = 0;
uint8_t key_up = 0;



uint8_t key_read(void)
{
	uint8_t temp = 0;
	
	if(HAL_GPIO_ReadPin(KEY1_GPIO_Port,KEY1_Pin) == GPIO_PIN_RESET)
		temp = 1;
	if(HAL_GPIO_ReadPin(KEY2_GPIO_Port,KEY2_Pin) == GPIO_PIN_RESET)
		temp = 2;
	if(HAL_GPIO_ReadPin(KEY3_GPIO_Port,KEY3_Pin) == GPIO_PIN_RESET)
		temp = 3;
	if(HAL_GPIO_ReadPin(KEY4_GPIO_Port,KEY4_Pin) == GPIO_PIN_RESET)
		temp = 4;
	return temp;
}

void key_proc(void)
{
	key_val = key_read();
	key_down = key_val & (key_val ^ key_old);
	key_up = ~key_val & (key_val ^ key_old);
	key_old = key_val;
	if(key_down==1)
	{
		// PE0按键按下，执行电机复位到初始位置（位置0）
		my_printf(&huart1, "按键复位开始...\r\n");
		process_reset_command();
		my_printf(&huart1, "按键复位完成\r\n");
	}
	else if(key_down==2)
	{
		// PE1按键按下，移动到左上角测试
		my_printf(&huart1, "移动到左上角测试开始...\r\n");
		if (start_move_to_left_top_corner()) {
			my_printf(&huart1, "移动到左上角测试启动成功\r\n");
		} else {
			my_printf(&huart1, "移动到左上角测试启动失败\r\n");
		}
	}
	else if(key_down==3)
	{
		// PE2按键按下，启动完整循迹：初始位置->左上角->右上角->右下角->左下角->左上角->初始位置
		my_printf(&huart1, "完整循迹开始...\r\n");

		// 检查数据有效性
		if (!latest_rect_coord.isValid || !latest_red_laser_coord.isValid) {
			my_printf(&huart1, "数据无效，循迹失败\r\n");
			return;
		}

		// 创建7点循迹路径
		Point path[7];

		// 1. 初始位置（当前激光位置）
		path[0].x = latest_red_laser_coord.x;
		path[0].y = latest_red_laser_coord.y;

		// 紧急修复：使用最简单安全的方法，直接按索引顺序使用四个顶点
		// 这样确保不会有算法错误，激光笔不会转动到天花板

		// 构建7点路径：初始位置 → 顶点0 → 顶点1 → 顶点2 → 顶点3 → 顶点0 → 初始位置
		path[1].x = latest_rect_coord.x[0];  // 第1个顶点
		path[1].y = latest_rect_coord.y[0];

		path[2].x = latest_rect_coord.x[1];  // 第2个顶点
		path[2].y = latest_rect_coord.y[1];

		path[3].x = latest_rect_coord.x[2];  // 第3个顶点
		path[3].y = latest_rect_coord.y[2];

		path[4].x = latest_rect_coord.x[3];  // 第4个顶点
		path[4].y = latest_rect_coord.y[3];

		path[5].x = latest_rect_coord.x[0];  // 回到第1个顶点
		path[5].y = latest_rect_coord.y[0];

		path[6].x = latest_red_laser_coord.x;  // 回到初始位置
		path[6].y = latest_red_laser_coord.y;

		// 启动7点循迹
		init_path_sequence(path, 7);
		border_ctrl.is_active = true;
		border_ctrl.state = BORDER_TRACE_MOVING;

		my_printf(&huart1, "7点循迹启动成功\r\n");
	}
	else if(key_down==4)
	{
		// PE3按键按下，紧急停止所有循迹功能
		my_printf(&huart1, "紧急停止所有循迹功能\r\n");
		stop_rectangle_trace();      // 停止原有循迹
		emergency_stop_border_trace(); // 紧急停止边框循迹
		my_printf(&huart1, "所有循迹功能已停止\r\n");
	}

}
